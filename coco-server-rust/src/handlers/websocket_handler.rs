use axum::{
    extract::{
        ws::{Message, WebSocket, WebSocketUpgrade},
        State,
    },
    response::Response,
};
use futures::stream::StreamExt;
use std::sync::Arc;
use tracing::{debug, error, info};

use crate::config::config_manager::ConfigManager;

/// WebSocket连接处理函数
pub async fn websocket_handler(
    ws: WebSocketUpgrade,
    State(config_manager): State<Arc<ConfigManager>>,
) -> Response {
    info!("Received WebSocket upgrade request");
    
    // 将WebSocket连接升级请求转换为响应
    ws.on_upgrade(|socket| handle_socket(socket, config_manager))
}

/// 处理WebSocket连接
async fn handle_socket(mut socket: WebSocket, config_manager: Arc<ConfigManager>) {
    info!("WebSocket connection established");
    
    // 发送欢迎消息
    if let Err(e) = socket
        .send(Message::Text("Connected to Coco Server WebSocket".to_string()))
        .await
    {
        error!("Failed to send welcome message: {}", e);
        return;
    }
    
    // 处理WebSocket消息循环
    while let Some(result) = socket.next().await {
        match result {
            Ok(message) => {
                if !handle_message(&mut socket, message, &config_manager).await {
                    break;
                }
            }
            Err(e) => {
                error!("WebSocket error: {}", e);
                break;
            }
        }
    }
    
    info!("WebSocket connection closed");
}

/// 处理单个WebSocket消息
async fn handle_message(
    socket: &mut WebSocket,
    message: Message,
    _config_manager: &Arc<ConfigManager>,
) -> bool {
    match message {
        Message::Text(text) => {
            debug!("Received text message: {}", text);
            
            // 回显消息
            let response = format!("Echo: {}", text);
            if let Err(e) = socket.send(Message::Text(response)).await {
                error!("Failed to send echo message: {}", e);
                return false;
            }
        }
        Message::Binary(data) => {
            debug!("Received binary message with {} bytes", data.len());
            
            // 回显二进制消息
            if let Err(e) = socket.send(Message::Binary(data)).await {
                error!("Failed to send binary echo message: {}", e);
                return false;
            }
        }
        Message::Close(frame) => {
            info!("Received close frame: {:?}", frame);
            // 发送关闭帧并退出循环
            if let Err(e) = socket.send(Message::Close(frame)).await {
                error!("Failed to send close frame: {}", e);
            }
            return false;
        }
        Message::Ping(data) => {
            debug!("Received ping with {} bytes", data.len());
            // 回复pong
            if let Err(e) = socket.send(Message::Pong(data)).await {
                error!("Failed to send pong: {}", e);
                return false;
            }
        }
        Message::Pong(_) => {
            debug!("Received pong");
            // Pong消息通常不需要特殊处理
        }
    }
    
    true
}

#[cfg(test)]
mod tests {
    use super::*;
    use axum::{
        body::Body,
        http::{Request, StatusCode},
        routing::get,
        Router,
    };
    use std::sync::Arc;
    use tower::ServiceExt;

    #[tokio::test]
    async fn test_websocket_route_exists() {
        // 创建一个模拟的配置管理器
        let config_manager = Arc::new(ConfigManager::new().unwrap());
        
        // 创建应用路由
        let app = Router::new()
            .route("/ws", get(websocket_handler))
            .with_state(config_manager);

        // 创建一个普通GET请求（不包含WebSocket握手头）
        let request = Request::builder()
            .uri("/ws")
            .body(Body::empty())
            .unwrap();

        // 发送请求
        let response = app.oneshot(request).await.unwrap();
        
        // 验证路由存在（不应该是404）
        assert_ne!(response.status(), StatusCode::NOT_FOUND);
    }
}